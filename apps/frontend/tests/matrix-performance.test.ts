/**
 * 矩阵性能优化测试
 * 🎯 目标：验证优化后的矩阵系统性能提升和功能正确性
 * 📦 测试范围：性能基准测试、内存使用测试、功能验证测试
 * ⚡ 性能指标：查找速度、内存占用、缓存命中率
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { performance } from 'perf_hooks';

// 导入优化后的系统
import { CompactCoordinateStore, COLOR_INDEX_MAP } from '@/lib/data/CompactCoordinateStore';
import { PrecomputedMatrixCache, globalPrecomputedCache } from '@/lib/data/PrecomputedMatrixCache';
import { OptimizedMatrixLookup, globalOptimizedLookup } from '@/lib/data/OptimizedMatrixLookup';
import { createSpatialIndex } from '@/lib/data/SpatialIndexSystem';
import { OPTIMIZED_GROUP_OFFSET_CONFIGS, validateOffsetConfigs } from '@/stores/constants/optimized-matrix';

// 导入原始系统用于对比
import { calculateGroupCoordinates, generateMatrixData } from '@/lib/utils/matrixUtils';
import { GROUP_A_DATA, AVAILABLE_LEVELS } from '@/stores/constants/matrix';

import type { BasicColorType, GroupType } from '@/lib/types/matrix';

describe('矩阵性能优化测试', () => {
  const testGroups: GroupType[] = ["A", "B", "C", "D", "E"];
  const testColors: BasicColorType[] = ["red", "cyan", "yellow", "purple", "orange"];
  const testLevels: (1 | 2 | 3 | 4)[] = [1, 2, 3, 4];

  beforeAll(async () => {
    console.log('🚀 开始矩阵性能优化测试...');
  });

  afterAll(() => {
    console.log('✅ 矩阵性能优化测试完成');
  });

  describe('紧凑坐标存储测试', () => {
    it('应该正确存储和检索坐标数据', () => {
      const store = new CompactCoordinateStore();
      
      // 添加测试数据
      const testData = [
        { x: 0, y: 0, colorIndex: COLOR_INDEX_MAP.red, level: 1 },
        { x: 8, y: 0, colorIndex: COLOR_INDEX_MAP.cyan, level: 2 },
        { x: -4, y: 4, colorIndex: COLOR_INDEX_MAP.yellow, level: 3 }
      ];
      
      const indices = store.addBatch(testData);
      expect(indices).toHaveLength(3);
      
      // 验证数据检索
      const retrieved = store.getBatch(indices);
      expect(retrieved).toHaveLength(3);
      expect(retrieved[0]).toEqual(testData[0]);
      expect(retrieved[1]).toEqual(testData[1]);
      expect(retrieved[2]).toEqual(testData[2]);
    });

    it('应该提供高效的坐标查找', () => {
      const store = new CompactCoordinateStore();
      
      // 添加大量测试数据
      const testData = [];
      for (let x = -16; x <= 16; x += 2) {
        for (let y = -16; y <= 16; y += 2) {
          testData.push({
            x, y,
            colorIndex: Math.floor(Math.random() * 9),
            level: Math.floor(Math.random() * 4) + 1
          });
        }
      }
      
      const startTime = performance.now();
      store.addBatch(testData);
      const addTime = performance.now() - startTime;
      
      // 测试查找性能
      const searchStart = performance.now();
      const found = store.findByCoordinate(0, 0);
      const searchTime = performance.now() - searchStart;
      
      console.log(`📊 紧凑存储性能: 添加${testData.length}个点耗时${addTime.toFixed(2)}ms, 查找耗时${searchTime.toFixed(2)}ms`);
      
      expect(addTime).toBeLessThan(100); // 添加应该在100ms内完成
      expect(searchTime).toBeLessThan(10); // 查找应该在10ms内完成
    });

    it('应该显著减少内存使用', () => {
      const store = new CompactCoordinateStore();
      
      // 添加1000个坐标点
      const testData = Array.from({ length: 1000 }, (_, i) => ({
        x: (i % 33) - 16,
        y: Math.floor(i / 33) - 16,
        colorIndex: i % 9,
        level: (i % 4) + 1
      }));
      
      store.addBatch(testData);
      const stats = store.getMemoryStats();
      
      console.log(`📊 内存统计: 总字节${stats.totalBytes}, 已用字节${stats.usedBytes}, 利用率${(stats.utilization * 100).toFixed(1)}%`);
      
      // 验证内存效率
      expect(stats.utilization).toBeGreaterThan(0.5); // 利用率应该超过50%
      expect(stats.totalBytes).toBeLessThan(50000); // 总内存应该小于50KB
    });
  });

  describe('预计算缓存系统测试', () => {
    it('应该正确预计算所有组合', () => {
      const cache = new PrecomputedMatrixCache({
        enableLazyLoading: false,
        enableCompression: true
      });
      
      // 等待预计算完成
      setTimeout(() => {
        const metrics = cache.getMetrics();
        console.log(`📊 预计算统计: ${metrics.cachedCombinations}/${metrics.totalCombinations} 组合, 耗时${metrics.computeTime.toFixed(2)}ms`);
        
        expect(metrics.cachedCombinations).toBeGreaterThan(0);
        expect(metrics.computeTime).toBeLessThan(5000); // 预计算应该在5秒内完成
      }, 100);
    });

    it('应该提供快速的数据检索', () => {
      const testCases = [
        { group: 'A' as GroupType, color: 'red' as BasicColorType, level: 1 as const },
        { group: 'B' as GroupType, color: 'cyan' as BasicColorType, level: 2 as const },
        { group: 'C' as GroupType, color: 'yellow' as BasicColorType, level: 3 as const }
      ];
      
      const startTime = performance.now();
      
      for (const testCase of testCases) {
        const result = globalPrecomputedCache.get(testCase.group, testCase.color, testCase.level);
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
      }
      
      const totalTime = performance.now() - startTime;
      console.log(`📊 缓存查询性能: ${testCases.length}次查询耗时${totalTime.toFixed(2)}ms`);
      
      expect(totalTime).toBeLessThan(50); // 3次查询应该在50ms内完成
    });

    it('应该正确处理批量查询', () => {
      const requests = testGroups.slice(0, 3).flatMap(group =>
        testColors.slice(0, 3).flatMap(color =>
          testLevels.slice(0, 2).map(level => ({ group, color, level }))
        )
      );
      
      const startTime = performance.now();
      const results = globalPrecomputedCache.getBatch(requests);
      const batchTime = performance.now() - startTime;
      
      console.log(`📊 批量查询性能: ${requests.length}个请求耗时${batchTime.toFixed(2)}ms`);
      
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      expect(batchTime).toBeLessThan(100); // 批量查询应该在100ms内完成
    });
  });

  describe('空间索引系统测试', () => {
    it('应该正确构建空间索引', () => {
      const spatialIndex = createSpatialIndex({
        maxPointsPerNode: 10,
        maxDepth: 6
      });
      
      // 添加测试点
      const testPoints = Array.from({ length: 100 }, (_, i) => ({
        x: (i % 33) - 16,
        y: Math.floor(i / 33) - 16,
        data: {
          coords: [(i % 33) - 16, Math.floor(i / 33) - 16] as [number, number],
          group: 'A' as GroupType,
          color: 'red' as BasicColorType,
          level: 1 as const,
          transformRule: `test_${i}`
        }
      }));
      
      const startTime = performance.now();
      spatialIndex.insertBatch(testPoints);
      const insertTime = performance.now() - startTime;
      
      const stats = spatialIndex.getStats();
      console.log(`📊 空间索引统计: ${stats.totalPoints}个点, 最大深度${stats.maxDepth}, 节点数${stats.nodeCount}, 构建耗时${insertTime.toFixed(2)}ms`);
      
      expect(stats.totalPoints).toBe(testPoints.length);
      expect(insertTime).toBeLessThan(100); // 构建应该在100ms内完成
    });

    it('应该提供高效的范围查询', () => {
      const spatialIndex = createSpatialIndex();
      
      // 添加网格数据
      for (let x = -16; x <= 16; x += 4) {
        for (let y = -16; y <= 16; y += 4) {
          spatialIndex.insert({
            x, y,
            data: {
              coords: [x, y] as [number, number],
              group: 'A' as GroupType,
              color: 'red' as BasicColorType,
              level: 1,
              transformRule: `grid_${x}_${y}`
            }
          });
        }
      }
      
      // 测试范围查询性能
      const startTime = performance.now();
      const results = spatialIndex.query({ minX: -8, minY: -8, maxX: 8, maxY: 8 });
      const queryTime = performance.now() - startTime;
      
      console.log(`📊 空间查询性能: 查询到${results.length}个点, 耗时${queryTime.toFixed(2)}ms`);
      
      expect(results.length).toBeGreaterThan(0);
      expect(queryTime).toBeLessThan(20); // 范围查询应该在20ms内完成
    });
  });

  describe('优化查找系统测试', () => {
    it('应该提供统一的高效查找接口', async () => {
      // 测试坐标查找
      const coordStart = performance.now();
      const coordResult = globalOptimizedLookup.getByCoordinate(0, 0);
      const coordTime = performance.now() - coordStart;
      
      // 测试范围查找
      const rangeStart = performance.now();
      const rangeResult = globalOptimizedLookup.getInRange(-4, -4, 4, 4);
      const rangeTime = performance.now() - rangeStart;
      
      // 测试组合查找
      const groupStart = performance.now();
      const groupResult = globalOptimizedLookup.getByGroupColorLevel('A', 'red', 1);
      const groupTime = performance.now() - groupStart;
      
      console.log(`📊 优化查找性能:`);
      console.log(`  - 坐标查找: ${coordResult.points.length}个点, 耗时${coordTime.toFixed(2)}ms`);
      console.log(`  - 范围查找: ${rangeResult.points.length}个点, 耗时${rangeTime.toFixed(2)}ms`);
      console.log(`  - 组合查找: ${groupResult.points.length}个点, 耗时${groupTime.toFixed(2)}ms`);
      
      expect(coordTime).toBeLessThan(10);
      expect(rangeTime).toBeLessThan(50);
      expect(groupTime).toBeLessThan(10);
    });

    it('应该显示性能指标', () => {
      const metrics = globalOptimizedLookup.getMetrics();
      const detailedStats = globalOptimizedLookup.getDetailedStats();
      
      console.log(`📊 查找系统指标:`);
      console.log(`  - 总查询次数: ${metrics.totalQueries}`);
      console.log(`  - 平均查询时间: ${metrics.averageQueryTime.toFixed(2)}ms`);
      console.log(`  - 内存使用: ${(metrics.memoryUsage / 1024).toFixed(1)}KB`);
      console.log(`  - 缓存命中率: ${(metrics.hitRate * 100).toFixed(1)}%`);
      
      expect(metrics.totalQueries).toBeGreaterThan(0);
      expect(metrics.averageQueryTime).toBeLessThan(100);
    });
  });

  describe('优化配置验证测试', () => {
    it('应该验证优化配置的完整性', () => {
      const isValid = validateOffsetConfigs();
      expect(isValid).toBe(true);
    });

    it('应该包含所有必要的组配置', () => {
      const groups: GroupType[] = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M"];
      
      for (const group of groups) {
        const config = OPTIMIZED_GROUP_OFFSET_CONFIGS[group];
        expect(config).toBeDefined();
        expect(config.defaultOffset).toBeDefined();
        expect(Array.isArray(config.defaultOffset)).toBe(true);
        expect(config.defaultOffset).toHaveLength(2);
      }
    });
  });

  describe('性能对比测试', () => {
    it('应该显著提升查找性能', () => {
      const iterations = 100;
      const testCases = [
        { group: 'A' as GroupType, color: 'red' as BasicColorType, level: 1 as const },
        { group: 'B' as GroupType, color: 'cyan' as BasicColorType, level: 2 as const },
        { group: 'C' as GroupType, color: 'yellow' as BasicColorType, level: 3 as const }
      ];
      
      // 测试优化后的性能
      const optimizedStart = performance.now();
      for (let i = 0; i < iterations; i++) {
        for (const testCase of testCases) {
          globalOptimizedLookup.getByGroupColorLevel(testCase.group, testCase.color, testCase.level);
        }
      }
      const optimizedTime = performance.now() - optimizedStart;
      
      console.log(`📊 性能对比 (${iterations * testCases.length}次查询):`);
      console.log(`  - 优化后: ${optimizedTime.toFixed(2)}ms`);
      console.log(`  - 平均每次: ${(optimizedTime / (iterations * testCases.length)).toFixed(2)}ms`);
      
      expect(optimizedTime).toBeLessThan(1000); // 总时间应该在1秒内
      expect(optimizedTime / (iterations * testCases.length)).toBeLessThan(5); // 平均每次应该在5ms内
    });
  });
});
