{"timestamp": "2025-07-31T06:12:47.690Z", "config": {"iterations": 1000, "warmupIterations": 100, "testDataSize": 1000, "enableMemoryProfiling": true, "outputPath": "./docs/report"}, "results": [{"testName": "紧凑存储-批量添加", "iterations": 100, "totalTime": 3.065706000000006, "averageTime": 0.03065706000000006, "minTime": 0.0074580000000139535, "maxTime": 0.3350419999999872, "throughput": 32618.91388150064}, {"testName": "紧凑存储-单点查找", "iterations": 1000, "totalTime": 3.824740999999648, "averageTime": 0.003824740999999648, "minTime": 0.0027079999999841675, "maxTime": 0.14962500000001455, "throughput": 261455.61228854244}, {"testName": "紧凑存储-范围查找", "iterations": 1000, "totalTime": 7.451809999999114, "averageTime": 0.007451809999999113, "minTime": 0.004457999999999629, "maxTime": 0.30262500000000614, "throughput": 134195.58469688828}, {"testName": "预计算缓存-单次查询", "iterations": 1000, "totalTime": 1.4483710000007193, "averageTime": 0.0014483710000007192, "minTime": 0.0002499999999940883, "maxTime": 0.04912500000000364, "throughput": 690430.835745471}, {"testName": "预计算缓存-批量查询", "iterations": 100, "totalTime": 0.7549629999998899, "averageTime": 0.007549629999998899, "minTime": 0.004958999999985281, "maxTime": 0.17833400000000665, "throughput": 132456.8223873416}, {"testName": "空间索引-构建", "iterations": 10, "totalTime": 5.304251000000022, "averageTime": 0.5304251000000022, "minTime": 0.3675000000000068, "maxTime": 1.3170840000000226, "throughput": 1885.2803157316573}, {"testName": "空间索引-点查询", "iterations": 1000, "totalTime": 4.681262999999831, "averageTime": 0.004681262999999831, "minTime": 0.0003329999999834854, "maxTime": 0.6520840000000021, "throughput": 213617.5643197223}, {"testName": "空间索引-范围查询", "iterations": 1000, "totalTime": 7.866435000000251, "averageTime": 0.007866435000000251, "minTime": 0.00029099999997583836, "maxTime": 0.7369589999999846, "throughput": 127122.3877143799}, {"testName": "优化查找-坐标查找", "iterations": 1000, "totalTime": 9.938396999999952, "averageTime": 0.009938396999999953, "minTime": 0.0012499999999988631, "maxTime": 1.3935840000000042, "throughput": 100619.84845242192}, {"testName": "优化查找-范围查找", "iterations": 1000, "totalTime": 17.58953100000093, "averageTime": 0.017589531000000932, "minTime": 0.010208000000005768, "maxTime": 0.6218749999999886, "throughput": 56851.99906694198}, {"testName": "优化查找-组合查找", "iterations": 1000, "totalTime": 10.72873200000052, "averageTime": 0.01072873200000052, "minTime": 0.00787499999995589, "maxTime": 0.3594580000000178, "throughput": 93207.65958176153}, {"testName": "内存使用-总计", "iterations": 1, "totalTime": 0, "averageTime": 0, "minTime": 0, "maxTime": 0, "memoryUsage": 71296, "throughput": 0}], "summary": {"totalTests": 12, "avgThroughput": "153705", "fastestTest": {"testName": "内存使用-总计", "iterations": 1, "totalTime": 0, "averageTime": 0, "minTime": 0, "maxTime": 0, "memoryUsage": 71296, "throughput": 0}, "slowestTest": {"testName": "空间索引-构建", "iterations": 10, "totalTime": 5.304251000000022, "averageTime": 0.5304251000000022, "minTime": 0.3675000000000068, "maxTime": 1.3170840000000226, "throughput": 1885.2803157316573}}}