/**
 * 智能缓存管理器
 * 🎯 核心价值：提供精确的缓存失效策略，替换简单的全量清空
 * 📦 功能范围：选择性失效、LRU策略、依赖管理、性能监控
 * ⚡ 性能优化：减少不必要的缓存清空，提高缓存命中率
 */

export interface CacheEntry<T = any> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccess: number;
  size: number;
  dependencies: Set<string>;
  tags: Set<string>;
  ttl?: number;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  evictions: number;
  hitRate: number;
  totalSize: number;
  memoryUsage: number;
  averageAccessTime: number;
}

export interface CacheConfig {
  maxSize: number;
  maxMemoryUsage: number; // bytes
  defaultTTL: number; // milliseconds
  enableLRU: boolean;
  enableMetrics: boolean;
  cleanupInterval: number; // milliseconds
}

/**
 * 智能缓存管理器类
 */
export class IntelligentCacheManager<T = any> {
  private cache = new Map<string, CacheEntry<T>>();
  private dependencies = new Map<string, Set<string>>();
  private tags = new Map<string, Set<string>>();
  private accessTimes = new Map<string, number>();
  private metrics: CacheMetrics;
  private config: CacheConfig;
  private cleanupTimer: number | null = null;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 1000,
      maxMemoryUsage: 50 * 1024 * 1024, // 50MB
      defaultTTL: 5 * 60 * 1000, // 5分钟
      enableLRU: true,
      enableMetrics: true,
      cleanupInterval: 60 * 1000, // 1分钟
      ...config
    };

    this.metrics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      hitRate: 0,
      totalSize: 0,
      memoryUsage: 0,
      averageAccessTime: 0
    };

    this.startCleanupTimer();
  }

  /**
   * 获取缓存条目
   */
  get(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      this.updateMetrics('miss');
      return null;
    }

    // 检查TTL
    if (entry.ttl && Date.now() - entry.timestamp > entry.ttl) {
      this.delete(key);
      this.updateMetrics('miss');
      return null;
    }

    // 更新访问信息
    entry.lastAccess = Date.now();
    entry.accessCount++;
    this.accessTimes.set(key, entry.lastAccess);

    this.updateMetrics('hit');
    return entry.value;
  }

  /**
   * 设置缓存条目
   */
  set(key: string, value: T, options: {
    dependencies?: string[];
    tags?: string[];
    ttl?: number;
  } = {}): void {
    const now = Date.now();
    const size = this.estimateSize(value);

    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      accessCount: 1,
      lastAccess: now,
      size,
      dependencies: new Set(options.dependencies || []),
      tags: new Set(options.tags || []),
      ttl: options.ttl || this.config.defaultTTL
    };

    // 检查是否需要清理空间
    this.ensureCapacity(size);

    // 设置缓存条目
    this.cache.set(key, entry);
    this.accessTimes.set(key, now);

    // 更新依赖关系
    if (options.dependencies) {
      options.dependencies.forEach(dep => {
        if (!this.dependencies.has(dep)) {
          this.dependencies.set(dep, new Set());
        }
        this.dependencies.get(dep)!.add(key);
      });
    }

    // 更新标签关系
    if (options.tags) {
      options.tags.forEach(tag => {
        if (!this.tags.has(tag)) {
          this.tags.set(tag, new Set());
        }
        this.tags.get(tag)!.add(key);
      });
    }

    this.updateMetrics('set');
  }

  /**
   * 删除缓存条目
   */
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // 清理依赖关系
    entry.dependencies.forEach(dep => {
      const dependentKeys = this.dependencies.get(dep);
      if (dependentKeys) {
        dependentKeys.delete(key);
        if (dependentKeys.size === 0) {
          this.dependencies.delete(dep);
        }
      }
    });

    // 清理标签关系
    entry.tags.forEach(tag => {
      const taggedKeys = this.tags.get(tag);
      if (taggedKeys) {
        taggedKeys.delete(key);
        if (taggedKeys.size === 0) {
          this.tags.delete(tag);
        }
      }
    });

    this.cache.delete(key);
    this.accessTimes.delete(key);
    return true;
  }

  /**
   * 按模式失效缓存
   */
  invalidateByPattern(pattern: string | RegExp): number {
    const keysToDelete: string[] = [];

    for (const [key] of this.cache) {
      const matches = typeof pattern === 'string'
        ? key.includes(pattern)
        : pattern.test(key);

      if (matches) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.length;
  }

  /**
   * 按依赖失效缓存
   */
  invalidateByDependency(dependency: string): number {
    const dependentKeys = this.dependencies.get(dependency);
    if (!dependentKeys) return 0;

    const keysToDelete = Array.from(dependentKeys);
    keysToDelete.forEach(key => this.delete(key));

    return keysToDelete.length;
  }

  /**
   * 按标签失效缓存
   */
  invalidateByTags(tags: string[]): number {
    const keysToDelete = new Set<string>();

    tags.forEach(tag => {
      const taggedKeys = this.tags.get(tag);
      if (taggedKeys) {
        taggedKeys.forEach(key => keysToDelete.add(key));
      }
    });

    keysToDelete.forEach(key => this.delete(key));
    return keysToDelete.size;
  }

  /**
   * LRU清理
   */
  evictLRU(count: number): number {
    if (this.cache.size <= count) return 0;

    const sortedEntries = Array.from(this.accessTimes.entries())
      .sort(([, a], [, b]) => a - b);

    const toEvict = sortedEntries.slice(0, count);
    let evicted = 0;

    toEvict.forEach(([key]) => {
      if (this.delete(key)) {
        evicted++;
        this.metrics.evictions++;
      }
    });

    return evicted;
  }

  /**
   * 确保缓存容量
   */
  private ensureCapacity(newEntrySize: number): void {
    // 检查数量限制
    if (this.cache.size >= this.config.maxSize) {
      this.evictLRU(Math.ceil(this.config.maxSize * 0.1)); // 清理10%
    }

    // 检查内存限制
    const currentMemoryUsage = this.calculateMemoryUsage();
    if (currentMemoryUsage + newEntrySize > this.config.maxMemoryUsage) {
      this.evictLRU(Math.ceil(this.cache.size * 0.2)); // 清理20%
    }
  }

  /**
   * 计算内存使用量
   */
  private calculateMemoryUsage(): number {
    let totalSize = 0;
    for (const [, entry] of this.cache) {
      totalSize += entry.size;
    }
    return totalSize;
  }

  /**
   * 估算对象大小
   */
  private estimateSize(value: any): number {
    if (value === null || value === undefined) return 0;

    if (typeof value === 'string') {
      return value.length * 2; // UTF-16
    }

    if (typeof value === 'number') {
      return 8; // 64-bit number
    }

    if (typeof value === 'boolean') {
      return 4;
    }

    if (Array.isArray(value)) {
      return value.reduce((sum, item) => sum + this.estimateSize(item), 0);
    }

    if (typeof value === 'object') {
      return Object.entries(value).reduce((sum, [key, val]) => {
        return sum + this.estimateSize(key) + this.estimateSize(val);
      }, 0);
    }

    return 100; // 默认估算
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(operation: 'hit' | 'miss' | 'set'): void {
    if (!this.config.enableMetrics) return;

    switch (operation) {
      case 'hit':
        this.metrics.hits++;
        break;
      case 'miss':
        this.metrics.misses++;
        break;
      case 'set':
        this.metrics.totalSize = this.cache.size;
        this.metrics.memoryUsage = this.calculateMemoryUsage();
        break;
    }

    // 计算命中率
    const total = this.metrics.hits + this.metrics.misses;
    this.metrics.hitRate = total > 0 ? this.metrics.hits / total : 0;
  }

  /**
   * 清理过期条目
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache) {
      if (entry.ttl && now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.delete(key));
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') {
      return;
    }

    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    this.cleanupTimer = window.setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * 获取缓存指标
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.dependencies.clear();
    this.tags.clear();
    this.accessTimes.clear();
    this.metrics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      hitRate: 0,
      totalSize: 0,
      memoryUsage: 0,
      averageAccessTime: 0
    };
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }
    this.clear();
  }
}

// 创建默认缓存管理器实例
export const defaultCacheManager = new IntelligentCacheManager();

// 缓存管理器工厂函数
export function createCacheManager<T = any>(config?: Partial<CacheConfig>): IntelligentCacheManager<T> {
  return new IntelligentCacheManager<T>(config);
}
