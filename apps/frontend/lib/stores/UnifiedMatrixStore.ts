/**
 * 统一矩阵状态管理器
 * 🎯 核心价值：整合所有状态管理到单一状态源，提供高性能的状态管理
 * 📦 功能范围：统一状态管理、计算属性、缓存优化、性能监控
 * 🔄 架构设计：基于Immer的不可变状态更新，支持计算属性自动派生
 */

import type {
  BasicColorType,
  BlackCellData,
  CellData,
  ColorLevel,
  ColorValue,
  ColorVisibility,
  GroupType,
  MatrixData
} from '@/lib/types/matrix';
import type {
  CacheState,
  CellRenderData,
  ComputedProperties,
  DeviceInfo,
  GridConfig,
  InteractionState,
  PerformanceConfig,
  PerformanceMetrics,
  StyleConfig,
  UnifiedMatrixState,
  UnifiedMatrixStore
} from '@/lib/types/unified-state';
import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 启用Immer的MapSet插件
enableMapSet();

// 导入现有的工具函数和常量
import {
  generateDefaultBlackCellData,
  generateDefaultColorVisibility,
  generateDefaultGroupVisibility,
  getAllColorTypes,
  getAllGroupTypes
} from '@/lib/utils/matrixHelpers';
import {
  generateGridData,
  generateMatrixData
} from '@/lib/utils/matrixUtils';
import {
  AVAILABLE_LEVELS,
  DEFAULT_COLOR_VALUES
} from '@/stores/constants/matrix';

// 默认配置
const defaultDeviceInfo: DeviceInfo = {
  width: 1920,
  height: 1080,
  devicePixelRatio: 1,
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  orientation: 'landscape'
};

const defaultPerformanceConfig: PerformanceConfig = {
  enableCache: true,
  cacheMaxSize: 1000,
  batchSize: 50,
  debounceDelay: 16,
  enableGPUAcceleration: true,
  enableVirtualization: false,
  performanceMode: 'balanced'
};

const defaultStyleConfig: StyleConfig = {
  fontSize: 12,
  fontFamily: 'Inter, system-ui, sans-serif',
  fontWeight: 400,
  primaryColor: '#3b82f6',
  secondaryColor: '#64748b',
  backgroundColor: '#ffffff',
  textColor: '#1f2937',
  borderColor: '#e5e7eb',
  cellSize: 24,
  cellGap: 1,
  borderRadius: 4,
  padding: 8,
  margin: 4,
  animationDuration: 200,
  animationEasing: 'ease-in-out',
  enableAnimations: true,
  showGrid: true,
  showBorders: true,
  enableShadows: false,
  opacity: 1.0
};

const defaultGridConfig: GridConfig = {
  displayMode: 'coordinates',
  colorModeEnabled: false,
  grayModeEnabled: false,
  fontSize: 12,
  matrixMargin: 10,
  gridColor: '#e5e7eb',
  cellShape: 'rounded',
  showBorders: true,
  enableAnimations: true
};

const defaultInteractionState: InteractionState = {
  hoveredElements: new Set(),
  clickedElements: new Set(),
  focusedElement: null,
  draggedElement: null,
  isLoading: false,
  error: null
};

const defaultCacheState: CacheState = {
  renderCache: new Map(),
  computeCache: new Map(),
  configCache: new Map(),
  cacheStats: {
    hits: 0,
    misses: 0,
    evictions: 0,
    hitRate: 0
  }
};

const defaultPerformanceMetrics: PerformanceMetrics = {
  renderTime: 0,
  updateCount: 0,
  frameRate: 60,
  memoryUsage: 0,
  cacheEfficiency: 0,
  initializationTime: 0,
  lastUpdateTime: Date.now()
};

// 计算属性实现
class ComputedPropertiesManager {
  private state: UnifiedMatrixState;
  private cache: Map<string, any> = new Map();
  private dependencies: Map<string, string[]> = new Map();

  constructor(state: UnifiedMatrixState) {
    this.state = state;
    this.setupDependencies();
  }

  private setupDependencies() {
    // 定义计算属性的依赖关系
    this.dependencies.set('visibleCells', ['gridData', 'colorVisibility', 'groupVisibility']);
    this.dependencies.set('renderData', ['visibleCells', 'gridConfig', 'styleConfig']);
    this.dependencies.set('activeCells', ['gridData', 'interactionState']);
    this.dependencies.set('statistics', ['matrixData', 'visibleCells']);
    this.dependencies.set('performanceStatus', ['performanceMetrics', 'performanceConfig']);
  }

  updateState(newState: UnifiedMatrixState) {
    this.state = newState;
    this.invalidateCache();
  }

  private invalidateCache() {
    // 简单的缓存失效策略 - 清空所有缓存
    this.cache.clear();
  }

  get visibleCells(): CellData[] {
    const cacheKey = 'visibleCells';
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const visibleCells = this.state.gridData.filter(cell => {
      // 检查颜色可见性
      const colorType = this.getCellColor(cell);
      if (colorType && !this.state.colorVisibility[colorType]?.showCells) {
        return false;
      }

      // 检查组可见性
      if (cell.group && !this.state.groupVisibility[cell.group]) {
        return false;
      }

      return true;
    });

    this.cache.set(cacheKey, visibleCells);
    return visibleCells;
  }

  get renderData(): Map<string, CellRenderData> {
    const cacheKey = 'renderData';
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const renderData = new Map<string, CellRenderData>();

    this.visibleCells.forEach(cell => {
      const renderInfo = this.generateCellRenderData(cell);
      renderData.set(cell.id, renderInfo);
    });

    this.cache.set(cacheKey, renderData);
    return renderData;
  }

  get activeCells(): Set<string> {
    const cacheKey = 'activeCells';
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const activeCells = new Set<string>();

    this.state.gridData.forEach(cell => {
      if (this.isCellActive(cell)) {
        activeCells.add(cell.id);
      }
    });

    this.cache.set(cacheKey, activeCells);
    return activeCells;
  }

  get statistics(): ComputedProperties['statistics'] {
    const cacheKey = 'statistics';
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const stats = {
      totalCells: this.state.gridData.length,
      visibleCells: this.visibleCells.length,
      activeCells: this.activeCells.size,
      colorDistribution: {} as Record<BasicColorType, number>,
      groupDistribution: {} as Record<GroupType, number>,
      levelDistribution: {} as Record<ColorLevel, number>
    };

    // 初始化分布统计
    getAllColorTypes().forEach(color => {
      stats.colorDistribution[color] = 0;
    });
    getAllGroupTypes().forEach(group => {
      stats.groupDistribution[group] = 0;
    });
    [1, 2, 3, 4].forEach(level => {
      stats.levelDistribution[level as ColorLevel] = 0;
    });

    // 计算分布
    if (this.state.matrixData) {
      Object.entries(this.state.matrixData.byColor).forEach(([color, points]) => {
        stats.colorDistribution[color as BasicColorType] = points.length;
      });
      Object.entries(this.state.matrixData.byGroup).forEach(([group, points]) => {
        stats.groupDistribution[group as GroupType] = points.length;
      });
      Object.entries(this.state.matrixData.byLevel).forEach(([level, points]) => {
        stats.levelDistribution[parseInt(level) as ColorLevel] = points.length;
      });
    }

    this.cache.set(cacheKey, stats);
    return stats;
  }

  get performanceStatus(): ComputedProperties['performanceStatus'] {
    const cacheKey = 'performanceStatus';
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const metrics = this.state.performanceMetrics;
    const config = this.state.performanceConfig;

    const status = {
      isOptimal: true,
      bottlenecks: [] as string[],
      recommendations: [] as string[]
    };

    // 性能检查
    if (metrics.renderTime > 16) {
      status.isOptimal = false;
      status.bottlenecks.push('渲染时间过长');
      status.recommendations.push('考虑启用缓存或降低渲染质量');
    }

    if (metrics.frameRate < 30) {
      status.isOptimal = false;
      status.bottlenecks.push('帧率过低');
      status.recommendations.push('优化渲染逻辑或启用GPU加速');
    }

    if (metrics.cacheEfficiency < 0.5) {
      status.isOptimal = false;
      status.bottlenecks.push('缓存效率低');
      status.recommendations.push('调整缓存策略或增加缓存大小');
    }

    this.cache.set(cacheKey, status);
    return status;
  }

  private getCellColor(cell: CellData): BasicColorType | null {
    // 根据单元格坐标查找颜色
    if (!this.state.matrixData) return null;

    const coordKey = `${cell.x},${cell.y}`;
    const dataPoints = this.state.matrixData.byCoordinate.get(coordKey);

    return dataPoints && dataPoints.length > 0 ? dataPoints[0].color : null;
  }

  private generateCellRenderData(cell: CellData): CellRenderData {
    const colorType = this.getCellColor(cell);
    const isActive = this.isCellActive(cell);
    const isHovered = this.state.interactionState.hoveredElements.has(cell.id);

    let color = cell.color;
    let content = '';

    // 根据显示模式生成内容
    switch (this.state.gridConfig.displayMode) {
      case 'coordinates':
        content = `${cell.x},${cell.y}`;
        break;
      case 'value':
        content = cell.colorMappingValue.toString();
        break;
      case 'color':
        // 使用单元格的颜色
        break;
    }

    // 生成样式
    const style: React.CSSProperties = {
      backgroundColor: color,
      fontSize: `${this.state.styleConfig.fontSize}px`,
      fontFamily: this.state.styleConfig.fontFamily,
      opacity: isActive ? 1 : this.state.styleConfig.opacity,
      transform: isHovered ? 'scale(1.05)' : 'scale(1)',
      transition: this.state.styleConfig.enableAnimations ?
        `all ${this.state.styleConfig.animationDuration}ms ${this.state.styleConfig.animationEasing}` :
        'none'
    };

    // 生成CSS类名
    const className = [
      'matrix-cell',
      `cell-${cell.id}`,
      isActive ? 'active' : 'inactive',
      isHovered ? 'hovered' : '',
      colorType ? `color-${colorType}` : ''
    ].filter(Boolean).join(' ');

    return {
      color,
      content,
      style,
      className,
      isActive,
      isVisible: true,
      priority: isActive ? 'high' : 'medium',
      renderHash: this.generateRenderHash(cell, colorType, isActive, isHovered)
    };
  }

  private isCellActive(cell: CellData): boolean {
    // 根据显示模式和配置判断单元格是否激活
    switch (this.state.gridConfig.displayMode) {
      case 'coordinates':
        return true;
      case 'color':
        return this.state.gridConfig.colorModeEnabled;
      case 'value':
        return true;
      default:
        return false;
    }
  }

  private generateRenderHash(
    cell: CellData,
    colorType: BasicColorType | null,
    isActive: boolean,
    isHovered: boolean
  ): string {
    return `${cell.id}-${colorType}-${isActive}-${isHovered}-${this.state.gridConfig.displayMode}-${this.state.lastModified}`;
  }
}

// 创建统一状态管理器
export const useUnifiedMatrixStore = create<UnifiedMatrixStore>()(
  persist(
    (set, get) => {
      let computedManager: ComputedPropertiesManager;
      let stateHistory: UnifiedMatrixState[] = [];
      const maxHistorySize = 10;

      // 初始状态
      const initialState: UnifiedMatrixState = {
        // 核心数据状态
        matrixData: generateMatrixData(true),
        gridData: generateGridData(),
        colorValues: DEFAULT_COLOR_VALUES,
        colorVisibility: generateDefaultColorVisibility(),
        colorLevelRules: AVAILABLE_LEVELS,
        blackCellData: generateDefaultBlackCellData(),
        groupVisibility: generateDefaultGroupVisibility(),

        // 配置状态
        gridConfig: defaultGridConfig,
        styleConfig: defaultStyleConfig,
        performanceConfig: defaultPerformanceConfig,

        // 运行时状态
        deviceInfo: defaultDeviceInfo,
        interactionState: defaultInteractionState,
        cacheState: defaultCacheState,
        performanceMetrics: defaultPerformanceMetrics,

        // 系统状态
        isInitialized: true,
        isHydrated: true,
        version: 1,
        lastModified: Date.now()
      };

      computedManager = new ComputedPropertiesManager(initialState);

      // 状态更新辅助函数
      const updateState = (updater: (draft: UnifiedMatrixState) => void) => {
        set(produce((state: UnifiedMatrixStore) => {
          // 提取状态数据
          const stateData: UnifiedMatrixState = {
            matrixData: state.matrixData,
            gridData: state.gridData,
            colorValues: state.colorValues,
            colorVisibility: state.colorVisibility,
            colorLevelRules: state.colorLevelRules,
            blackCellData: state.blackCellData,
            groupVisibility: state.groupVisibility,
            gridConfig: state.gridConfig,
            styleConfig: state.styleConfig,
            performanceConfig: state.performanceConfig,
            deviceInfo: state.deviceInfo,
            interactionState: state.interactionState,
            cacheState: state.cacheState,
            performanceMetrics: state.performanceMetrics,
            isInitialized: state.isInitialized(),
            isHydrated: state.isHydrated(),
            version: state.version,
            lastModified: state.lastModified
          };

          // 应用更新
          updater(stateData);

          // 将更新后的状态数据复制回 store
          Object.assign(state, stateData);
        }));

        // 更新计算属性管理器
        const newState = get();
        const stateSnapshot = newState.getSnapshot();
        computedManager.updateState(stateSnapshot);

        // 记录状态历史
        stateHistory.push(stateSnapshot);
        if (stateHistory.length > maxHistorySize) {
          stateHistory.shift();
        }
      };

      // 性能监控辅助函数
      const measurePerformance = <T>(operation: () => T, operationName: string): T => {
        const startTime = performance.now();
        const result = operation();
        const endTime = performance.now();
        const duration = endTime - startTime;

        // 更新性能指标
        updateState(draft => {
          draft.performanceMetrics.renderTime = duration;
          draft.performanceMetrics.updateCount += 1;
          draft.performanceMetrics.lastUpdateTime = Date.now();
        });

        if (process.env.NODE_ENV === 'development' && duration > 10) {
          console.warn(`⚠️ [UnifiedStore] ${operationName} 耗时较长: ${duration.toFixed(2)}ms`);
        }

        return result;
      };

      return {
        // 核心数据状态
        matrixData: initialState.matrixData,
        gridData: initialState.gridData,
        colorValues: initialState.colorValues,
        colorVisibility: initialState.colorVisibility,
        colorLevelRules: initialState.colorLevelRules,
        blackCellData: initialState.blackCellData,
        groupVisibility: initialState.groupVisibility,

        // 配置状态
        gridConfig: initialState.gridConfig,
        styleConfig: initialState.styleConfig,
        performanceConfig: initialState.performanceConfig,

        // 运行时状态
        deviceInfo: initialState.deviceInfo,
        interactionState: initialState.interactionState,
        cacheState: initialState.cacheState,
        performanceMetrics: initialState.performanceMetrics,

        // 系统状态
        version: initialState.version,
        lastModified: initialState.lastModified,

        // 计算属性（只读）
        get computed(): ComputedProperties {
          return {
            visibleCells: computedManager.visibleCells,
            renderData: computedManager.renderData,
            activeCells: computedManager.activeCells,
            statistics: computedManager.statistics,
            performanceStatus: computedManager.performanceStatus
          };
        },

        // 矩阵数据操作
        regenerateMatrixData: () => {
          measurePerformance(() => {
            updateState(draft => {
              draft.matrixData = generateMatrixData(true);
              draft.lastModified = Date.now();
            });
          }, 'regenerateMatrixData');
        },

        initializeMatrixData: () => {
          measurePerformance(() => {
            const state = get();
            if (!state.matrixData || state.matrixData.byCoordinate.size === 0) {
              updateState(draft => {
                draft.matrixData = generateMatrixData(true);
                draft.isInitialized = true;
                draft.isHydrated = true;
                draft.performanceMetrics.initializationTime = performance.now();
                draft.lastModified = Date.now();
              });
            }
          }, 'initializeMatrixData');
        },

        updateMatrixData: (data: MatrixData) => {
          updateState(draft => {
            draft.matrixData = data;
            draft.lastModified = Date.now();
          });
        },

        // 网格数据操作
        regenerateGridData: () => {
          updateState(draft => {
            draft.gridData = generateGridData();
            draft.lastModified = Date.now();
          });
        },

        updateCellData: (index: number, updates: Partial<CellData>) => {
          updateState(draft => {
            if (index >= 0 && index < draft.gridData.length) {
              Object.assign(draft.gridData[index], updates);
              draft.lastModified = Date.now();
            }
          });
        },

        batchUpdateCells: (updates: Array<{ index: number; data: Partial<CellData> }>) => {
          updateState(draft => {
            updates.forEach(({ index, data }) => {
              if (index >= 0 && index < draft.gridData.length) {
                Object.assign(draft.gridData[index], data);
              }
            });
            draft.lastModified = Date.now();
          });
        },

        // 配置操作
        updateGridConfig: (updates: Partial<GridConfig>) => {
          updateState(draft => {
            Object.assign(draft.gridConfig, updates);
            draft.lastModified = Date.now();
          });
        },

        updateStyleConfig: (updates: Partial<StyleConfig>) => {
          updateState(draft => {
            Object.assign(draft.styleConfig, updates);
            draft.lastModified = Date.now();
          });
        },

        updatePerformanceConfig: (updates: Partial<PerformanceConfig>) => {
          updateState(draft => {
            Object.assign(draft.performanceConfig, updates);
            draft.lastModified = Date.now();
          });
        },

        // 可见性控制
        setColorVisibility: (colorType: BasicColorType, visibility: Partial<ColorVisibility>) => {
          updateState(draft => {
            Object.assign(draft.colorVisibility[colorType], visibility);
            draft.lastModified = Date.now();
          });
        },

        setGroupVisibility: (group: GroupType, visible: boolean) => {
          updateState(draft => {
            draft.groupVisibility[group] = visible;
            draft.lastModified = Date.now();
          });
        },

        toggleColorLevel: (colorType: BasicColorType, level: ColorLevel) => {
          updateState(draft => {
            const visibility = draft.colorVisibility[colorType];
            const levelKey = `showLevel${level}` as keyof ColorVisibility;
            if (levelKey in visibility) {
              (visibility as any)[levelKey] = !(visibility as any)[levelKey];
              draft.lastModified = Date.now();
            }
          });
        },

        // 交互状态
        setHovered: (elementId: string, hovered: boolean) => {
          updateState(draft => {
            if (hovered) {
              draft.interactionState.hoveredElements.add(elementId);
            } else {
              draft.interactionState.hoveredElements.delete(elementId);
            }
            draft.lastModified = Date.now();
          });
        },

        setClicked: (elementId: string, clicked: boolean) => {
          updateState(draft => {
            if (clicked) {
              draft.interactionState.clickedElements.add(elementId);
            } else {
              draft.interactionState.clickedElements.delete(elementId);
            }
            draft.lastModified = Date.now();
          });
        },

        setFocused: (elementId: string | null) => {
          updateState(draft => {
            draft.interactionState.focusedElement = elementId;
            draft.lastModified = Date.now();
          });
        },

        setLoading: (loading: boolean) => {
          updateState(draft => {
            draft.interactionState.isLoading = loading;
            draft.lastModified = Date.now();
          });
        },

        setError: (error: string | null) => {
          updateState(draft => {
            draft.interactionState.error = error;
            draft.lastModified = Date.now();
          });
        },

        // 缓存管理
        clearCache: (type?: 'render' | 'compute' | 'config' | 'all') => {
          updateState(draft => {
            switch (type) {
              case 'render':
                draft.cacheState.renderCache.clear();
                break;
              case 'compute':
                draft.cacheState.computeCache.clear();
                break;
              case 'config':
                draft.cacheState.configCache.clear();
                break;
              default:
                draft.cacheState.renderCache.clear();
                draft.cacheState.computeCache.clear();
                draft.cacheState.configCache.clear();
            }
            draft.lastModified = Date.now();
          });
        },

        invalidateCache: (pattern?: string) => {
          updateState(draft => {
            if (pattern) {
              // 根据模式清理特定缓存
              const regex = new RegExp(pattern);
              for (const [key] of draft.cacheState.renderCache) {
                if (regex.test(key)) {
                  draft.cacheState.renderCache.delete(key);
                }
              }
            } else {
              // 清理所有缓存
              draft.cacheState.renderCache.clear();
              draft.cacheState.computeCache.clear();
              draft.cacheState.configCache.clear();
            }
            draft.lastModified = Date.now();
          });
        },

        // 性能优化
        optimizePerformance: () => {
          measurePerformance(() => {
            updateState(draft => {
              // 清理过期缓存
              const now = Date.now();
              const maxAge = 5 * 60 * 1000; // 5分钟

              // 这里可以添加更多性能优化逻辑
              draft.lastModified = now;
            });
          }, 'optimizePerformance');
        },

        updatePerformanceMetrics: (metrics: Partial<PerformanceMetrics>) => {
          updateState(draft => {
            Object.assign(draft.performanceMetrics, metrics);
            draft.lastModified = Date.now();
          });
        },

        // 批量操作
        batchUpdate: (updates: Array<() => void>) => {
          measurePerformance(() => {
            updateState(draft => {
              // 在单个事务中执行所有更新
              updates.forEach(update => update());
              draft.lastModified = Date.now();
            });
          }, 'batchUpdate');
        },

        // 批量颜色可见性控制
        toggleAllColorCells: (show: boolean) => {
          updateState(draft => {
            Object.keys(draft.colorVisibility).forEach(colorType => {
              draft.colorVisibility[colorType as BasicColorType].showCells = show;
            });
            draft.lastModified = Date.now();
          });
        },

        // 批量组可见性控制
        toggleAllGroups: (show: boolean) => {
          updateState(draft => {
            Object.keys(draft.groupVisibility).forEach(group => {
              draft.groupVisibility[group as GroupType] = show;
            });
            draft.lastModified = Date.now();
          });
        },

        // 黑色格子数据操作
        setBlackCellData: (data: BlackCellData) => {
          updateState(draft => {
            draft.blackCellData = data;
            draft.lastModified = Date.now();
          });
        },

        toggleBlackCellVisibility: () => {
          updateState(draft => {
            draft.blackCellData.visibility = !draft.blackCellData.visibility;
            draft.lastModified = Date.now();
          });
        },

        // 颜色值更新
        updateColorValues: (colorType: BasicColorType, values: Partial<ColorValue>) => {
          updateState(draft => {
            Object.assign(draft.colorValues[colorType], values);
            draft.lastModified = Date.now();
          });
        },

        // 组可见性切换
        toggleGroupVisibility: (group: GroupType) => {
          updateState(draft => {
            draft.groupVisibility[group] = !draft.groupVisibility[group];
            draft.lastModified = Date.now();
          });
        },

        resetToDefaults: () => {
          updateState(draft => {
            // 重置到默认状态
            Object.assign(draft, {
              ...initialState,
              matrixData: generateMatrixData(true),
              gridData: generateGridData(),
              isInitialized: true,
              isHydrated: true,
              lastModified: Date.now()
            });
          });
        },

        // 状态选择器
        getMatrixData: () => get().matrixData,
        getGridData: () => get().gridData,
        getCellAt: (x: number, y: number) => {
          const gridData = get().gridData;
          return gridData.find(cell => cell.x === x && cell.y === y) || null;
        },
        getDataPointsAt: (x: number, y: number) => {
          const matrixData = get().matrixData;
          if (!matrixData) return [];
          const coordKey = `${x},${y}`;
          return matrixData.byCoordinate.get(coordKey) || [];
        },
        getGridConfig: () => get().gridConfig,
        getStyleConfig: () => get().styleConfig,
        getPerformanceConfig: () => get().performanceConfig,
        getVisibleCells: () => computedManager.visibleCells,
        getRenderData: () => computedManager.renderData,
        getActiveCells: () => computedManager.activeCells,
        getStatistics: () => computedManager.statistics,
        isInitialized: () => get().isInitialized,
        isHydrated: () => get().isHydrated,
        isLoading: () => get().interactionState.isLoading,
        hasError: () => get().interactionState.error !== null,
        getPerformanceMetrics: () => get().performanceMetrics,
        getCacheStats: () => get().cacheState.cacheStats,

        // 订阅机制
        subscribe: (listener: (state: UnifiedMatrixState) => void) => {
          return get().subscribe?.(listener) || (() => { });
        },

        // 状态快照
        getSnapshot: () => {
          const state = get();
          return {
            // 核心数据状态
            matrixData: state.matrixData,
            gridData: state.gridData,
            colorValues: state.colorValues,
            colorVisibility: state.colorVisibility,
            colorLevelRules: state.colorLevelRules,
            blackCellData: state.blackCellData,
            groupVisibility: state.groupVisibility,

            // 配置状态
            gridConfig: state.gridConfig,
            styleConfig: state.styleConfig,
            performanceConfig: state.performanceConfig,

            // 运行时状态
            deviceInfo: state.deviceInfo,
            interactionState: state.interactionState,
            cacheState: state.cacheState,
            performanceMetrics: state.performanceMetrics,

            // 系统状态
            isInitialized: state.isInitialized(),
            isHydrated: state.isHydrated(),
            version: state.version,
            lastModified: state.lastModified
          };
        },

        restoreSnapshot: (snapshot: UnifiedMatrixState) => {
          updateState(draft => {
            Object.assign(draft, snapshot);
          });
        },

        // 调试工具
        debug: {
          getStateHistory: () => [...stateHistory],
          exportState: () => JSON.stringify(get(), null, 2),
          importState: (stateJson: string) => {
            try {
              const state = JSON.parse(stateJson);
              set(state);
              computedManager.updateState(state);
            } catch (error) {
              console.error('Failed to import state:', error);
            }
          }
        }
      } as unknown as UnifiedMatrixStore;
    },
    {
      name: 'unified-matrix-store',
      version: 1,
      partialize: (state) => ({
        // 只持久化必要的状态
        matrixData: state.matrixData,
        colorValues: state.colorValues,
        colorVisibility: state.colorVisibility,
        colorLevelRules: state.colorLevelRules,
        blackCellData: state.blackCellData,
        groupVisibility: state.groupVisibility,
        gridConfig: state.gridConfig,
        styleConfig: state.styleConfig,
        performanceConfig: state.performanceConfig,
        isInitialized: state.isInitialized,
        version: state.version
      }),
      migrate: (persistedState: any, version: number) => {
        // 处理状态迁移
        if (version === 0) {
          return {
            ...persistedState,
            version: 1,
            lastModified: Date.now()
          };
        }
        return persistedState;
      }
    }
  )
);