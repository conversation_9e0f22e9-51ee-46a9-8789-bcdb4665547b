/**
 * 矩阵系统类型定义
 * 🎯 职责：矩阵数据结构、组类型、坐标系统等核心类型定义
 * 📦 重构来源：从 basicDataStore.ts 中提取的类型定义
 * ✅ 统一管理：与现有 color.ts 类型协调，避免重复定义
 */

import type { BasicColorType } from './color';

// 重新导出颜色类型，保持向后兼容
export type { BasicColorType } from './color';

// 颜色级别常量和类型
export const COLOR_LEVELS = [1, 2, 3, 4] as const;
export type ColorLevel = typeof COLOR_LEVELS[number];

// 组类型 - A到M共13个组
export type GroupType = "A" | "B" | "C" | "D" | "E" | "F" | "G" | "H" | "I" | "J" | "K" | "L" | "M";

// 统一的矩阵数据点接口
export interface MatrixDataPoint {
  coords: [number, number];
  level: 1 | 2 | 3 | 4;
  color: BasicColorType;
  group: GroupType;
  transformRule?: string;
}

// 颜色值定义
export interface ColorValue {
  name: string;
  hex: string;
  rgb: [number, number, number];
  hsl: [number, number, number];
  mappingValue?: number; // 颜色映射值 (黑色没有mappingValue)
}

// 简化的颜色可见性接口
export interface ColorVisibility {
  showCells: boolean;
  showLevel1?: boolean;
  showLevel2?: boolean;
  showLevel3?: boolean;
  showLevel4?: boolean;
}

// 黑色格子数据
export interface BlackCellData {
  coordinates: { coords: [number, number]; letter: string }[];
  visibility: boolean;
}

// 高效的矩阵数据索引结构
export interface MatrixData {
  // 按坐标索引的数据点 - 支持同一坐标多个数据点（多组叠加）
  byCoordinate: Map<string, MatrixDataPoint[]>; // key: "x,y"

  // 按组索引的数据点
  byGroup: Record<GroupType, MatrixDataPoint[]>;

  // 按颜色索引的数据点
  byColor: Record<BasicColorType, MatrixDataPoint[]>;

  // 按级别索引的数据点
  byLevel: Record<1 | 2 | 3 | 4, MatrixDataPoint[]>;
}

// 网格渲染用的单元格数据接口 - 兼容现有类型定义
export interface CellData {
  id: string;                    // 单元格唯一标识符
  row: number;                   // 网格行位置 (0-based索引)
  col: number;                   // 网格列位置 (0-based索引)
  x: number;                     // 以中心为(0,0)的X坐标
  y: number;                     // 以中心为(0,0)的Y坐标
  index: number;                 // 单元格序列编号 (0-based)
  color: string;                 // 当前显示颜色值 (十六进制格式)
  colorMappingValue: number;     // 用于颜色计算的数值
  level: number;                 // 单元格层级 (1-based)
  group: GroupType | null;       // 单元格所属组别 (A-M字母组，黑色单元格为null)
  isActive?: boolean;            // 单元格激活状态 (可选，默认为false)

  // 保持向后兼容的字段
  number: number;                // 兼容现有代码 (1-based)
}

// 组偏移配置接口
export interface GroupOffsetConfig {
  defaultOffset: [number, number];
  level1Offsets?: Partial<Record<BasicColorType, [number, number]>>;
  levelOffsets?: Partial<Record<1 | 2 | 3 | 4, Partial<Record<BasicColorType, [number, number]>>>>;
}

// Store状态接口
export interface BasicDataState {
  // 完整的矩阵数据 - 支持null状态以处理hydration
  matrixData: MatrixData | null;

  // 网格渲染数据
  gridData: CellData[];

  // 颜色值定义
  colorValues: Record<BasicColorType, ColorValue>;

  // 颜色可见性控制
  colorVisibility: Record<BasicColorType, ColorVisibility>;

  // 颜色级别规则
  colorLevelRules: Record<BasicColorType, number[]>;

  // 黑色格子数据
  blackCellData: BlackCellData;

  // 组可见性控制
  groupVisibility: Record<GroupType, boolean>;

  // hydration状态跟踪
  _isHydrated?: boolean;
}

// Store操作接口
export interface BasicDataActions {
  // 矩阵数据操作
  regenerateMatrixData: () => void;
  getMatrixData: () => MatrixData | null;
  getDataPointsAt: (x: number, y: number) => MatrixDataPoint[];
  getGroupData: (group: GroupType) => MatrixDataPoint[];
  getColorData: (color: BasicColorType) => MatrixDataPoint[];
  getLevelData: (level: 1 | 2 | 3 | 4) => MatrixDataPoint[];

  // 初始化数据（用于hydration后）
  initializeMatrixData: () => void;

  // 网格数据操作
  regenerateGridData: () => void;
  getGridData: () => CellData[];
  updateCellData: (index: number, updates: Partial<CellData>) => void;
  getCellAt: (x: number, y: number) => CellData | null;

  // 颜色值操作
  updateColorValues: (colorType: BasicColorType, values: Partial<ColorValue>) => void;
  getColorValue: (colorType: BasicColorType) => ColorValue;

  // 可见性控制
  setColorVisibility: (colorType: BasicColorType, visibility: Partial<ColorVisibility>) => void;
  toggleColorLevel: (colorType: BasicColorType, level: 1 | 2 | 3 | 4) => void;
  setGroupVisibility: (group: GroupType, visible: boolean) => void;
  toggleGroupVisibility: (group: GroupType) => void;

  // 黑色格子数据
  setBlackCellData: (data: BlackCellData) => void;
  toggleBlackCellVisibility: () => void;

  // 批量操作
  toggleAllColorCells: (show: boolean) => void;
  toggleAllGroups: (show: boolean) => void;
  resetToDefaults: () => void;

  // 常量访问器
  getAvailableLevels: (colorType: BasicColorType) => number[];
  getAllColorTypes: () => BasicColorType[];
  getAllGroupTypes: () => GroupType[];
}

export type BasicDataStore = BasicDataState & BasicDataActions;
