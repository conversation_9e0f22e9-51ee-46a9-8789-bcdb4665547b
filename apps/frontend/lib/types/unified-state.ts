/**
 * 统一状态管理类型定义
 * 🎯 核心价值：整合所有状态管理到单一状态源，提供类型安全的状态管理
 * 📦 功能范围：统一状态接口、计算属性、状态更新机制
 * 🔄 架构设计：基于Immer的不可变状态更新，支持计算属性自动派生
 */

import type { BaseDisplayMode } from '@/components/grid-system/types';
import type {
  BasicColorType,
  BlackCellData,
  CellData,
  ColorLevel,
  ColorValue,
  ColorVisibility,
  GroupType,
  MatrixData
} from './matrix';

// 设备信息接口
export interface DeviceInfo {
  width: number;
  height: number;
  devicePixelRatio: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
}

// 性能配置接口
export interface PerformanceConfig {
  enableCache: boolean;
  cacheMaxSize: number;
  batchSize: number;
  debounceDelay: number;
  enableGPUAcceleration: boolean;
  enableVirtualization: boolean;
  performanceMode: 'high' | 'balanced' | 'low' | 'auto';
}

// 样式配置接口
export interface StyleConfig {
  // 字体配置
  fontSize: number;
  fontFamily: string;
  fontWeight: number;

  // 颜色配置
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;

  // 布局配置
  cellSize: number;
  cellGap: number;
  borderRadius: number;
  padding: number;
  margin: number;

  // 动画配置
  animationDuration: number;
  animationEasing: string;
  enableAnimations: boolean;

  // 其他配置
  showGrid: boolean;
  showBorders: boolean;
  enableShadows: boolean;
  opacity: number;
}

// 网格配置接口
export interface GridConfig {
  displayMode: BaseDisplayMode;
  colorModeEnabled: boolean;
  grayModeEnabled: boolean;
  fontSize: number;
  matrixMargin: number;
  gridColor: string;
  cellShape: 'rounded' | 'circle' | 'square';
  showBorders: boolean;
  enableAnimations: boolean;
}

// 交互状态接口
export interface InteractionState {
  hoveredElements: Set<string>;
  clickedElements: Set<string>;
  focusedElement: string | null;
  draggedElement: string | null;
  isLoading: boolean;
  error: string | null;
}

// 缓存状态接口
export interface CacheState {
  renderCache: Map<string, any>;
  computeCache: Map<string, any>;
  configCache: Map<string, any>;
  cacheStats: {
    hits: number;
    misses: number;
    evictions: number;
    hitRate: number;
  };
}

// 性能指标接口
export interface PerformanceMetrics {
  renderTime: number;
  updateCount: number;
  frameRate: number;
  memoryUsage: number;
  cacheEfficiency: number;
  initializationTime: number;
  lastUpdateTime: number;
}

// 统一状态接口
export interface UnifiedMatrixState {
  // 核心数据状态
  matrixData: MatrixData | null;
  gridData: CellData[];
  colorValues: Record<BasicColorType, ColorValue>;
  colorVisibility: Record<BasicColorType, ColorVisibility>;
  colorLevelRules: Record<BasicColorType, number[]>;
  blackCellData: BlackCellData;
  groupVisibility: Record<GroupType, boolean>;

  // 配置状态
  gridConfig: GridConfig;
  styleConfig: StyleConfig;
  performanceConfig: PerformanceConfig;

  // 运行时状态
  deviceInfo: DeviceInfo;
  interactionState: InteractionState;
  cacheState: CacheState;
  performanceMetrics: PerformanceMetrics;

  // 系统状态
  isInitialized: boolean;
  isHydrated: boolean;
  version: number;
  lastModified: number;
}

// 计算属性接口
export interface ComputedProperties {
  // 可见单元格
  visibleCells: CellData[];

  // 渲染数据
  renderData: Map<string, CellRenderData>;

  // 活跃单元格
  activeCells: Set<string>;

  // 统计信息
  statistics: {
    totalCells: number;
    visibleCells: number;
    activeCells: number;
    colorDistribution: Record<BasicColorType, number>;
    groupDistribution: Record<GroupType, number>;
    levelDistribution: Record<ColorLevel, number>;
  };

  // 性能状态
  performanceStatus: {
    isOptimal: boolean;
    bottlenecks: string[];
    recommendations: string[];
  };
}

// 单元格渲染数据接口
export interface CellRenderData {
  color: string | null;
  content: string | null;
  style: React.CSSProperties;
  className: string;
  isActive: boolean;
  isVisible: boolean;
  priority: 'high' | 'medium' | 'low';
  renderHash: string;
}

// 状态更新操作接口
export interface StateUpdateActions {
  // 矩阵数据操作
  regenerateMatrixData: () => void;
  initializeMatrixData: () => void;
  updateMatrixData: (data: MatrixData) => void;

  // 网格数据操作
  regenerateGridData: () => void;
  updateCellData: (index: number, updates: Partial<CellData>) => void;
  batchUpdateCells: (updates: Array<{ index: number; data: Partial<CellData> }>) => void;

  // 配置操作
  updateGridConfig: (updates: Partial<GridConfig>) => void;
  updateStyleConfig: (updates: Partial<StyleConfig>) => void;
  updatePerformanceConfig: (updates: Partial<PerformanceConfig>) => void;

  // 可见性控制
  setColorVisibility: (colorType: BasicColorType, visibility: Partial<ColorVisibility>) => void;
  setGroupVisibility: (group: GroupType, visible: boolean) => void;
  toggleColorLevel: (colorType: BasicColorType, level: ColorLevel) => void;

  // 交互状态
  setHovered: (elementId: string, hovered: boolean) => void;
  setClicked: (elementId: string, clicked: boolean) => void;
  setFocused: (elementId: string | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // 缓存管理
  clearCache: (type?: 'render' | 'compute' | 'config' | 'all') => void;
  invalidateCache: (pattern?: string) => void;

  // 性能优化
  optimizePerformance: () => void;
  updatePerformanceMetrics: (metrics: Partial<PerformanceMetrics>) => void;

  // 批量操作
  batchUpdate: (updates: Array<() => void>) => void;
  resetToDefaults: () => void;

  // 兼容性方法
  toggleAllColorCells: (show: boolean) => void;
  toggleAllGroups: (show: boolean) => void;
  setBlackCellData: (data: BlackCellData) => void;
  toggleBlackCellVisibility: () => void;
  updateColorValues: (colorType: BasicColorType, values: Partial<ColorValue>) => void;
  toggleGroupVisibility: (group: GroupType) => void;
}

// 状态选择器接口
export interface StateSelectors {
  // 数据选择器
  getMatrixData: () => MatrixData | null;
  getGridData: () => CellData[];
  getCellAt: (x: number, y: number) => CellData | null;
  getDataPointsAt: (x: number, y: number) => any[];

  // 配置选择器
  getGridConfig: () => GridConfig;
  getStyleConfig: () => StyleConfig;
  getPerformanceConfig: () => PerformanceConfig;

  // 计算属性选择器
  getVisibleCells: () => CellData[];
  getRenderData: () => Map<string, CellRenderData>;
  getActiveCells: () => Set<string>;
  getStatistics: () => ComputedProperties['statistics'];

  // 状态查询
  isInitialized: () => boolean;
  isHydrated: () => boolean;
  isLoading: () => boolean;
  hasError: () => boolean;

  // 性能查询
  getPerformanceMetrics: () => PerformanceMetrics;
  getCacheStats: () => CacheState['cacheStats'];
}

// 完整的统一状态管理接口
export interface UnifiedMatrixStore extends
  Omit<UnifiedMatrixState, 'isInitialized' | 'isHydrated'>,
  StateUpdateActions,
  StateSelectors {

  // 计算属性（只读）
  readonly computed: ComputedProperties;

  // 订阅机制
  subscribe: (listener: (state: UnifiedMatrixState) => void) => () => void;

  // 状态快照
  getSnapshot: () => UnifiedMatrixState;

  // 状态恢复
  restoreSnapshot: (snapshot: UnifiedMatrixState) => void;

  // 调试工具
  debug: {
    getStateHistory: () => UnifiedMatrixState[];
    exportState: () => string;
    importState: (stateJson: string) => void;
  };
}

// 向后兼容的Hook接口
export interface LegacyHookInterface {
  // basicDataStore兼容接口
  matrixData: MatrixData | null;
  gridData: CellData[];
  colorValues: Record<BasicColorType, ColorValue>;
  colorVisibility: Record<BasicColorType, ColorVisibility>;
  regenerateMatrixData: () => void;
  initializeMatrixData: () => void;

  // gridConfigStore兼容接口
  baseDisplayMode: BaseDisplayMode;
  gridConfig: GridConfig;
  colorModeEnabled: boolean;
  setDisplayMode: (mode: BaseDisplayMode) => void;
  setColorModeEnabled: (enabled: boolean) => void;

  // styleStore兼容接口
  config: StyleConfig;
  theme: 'light' | 'dark' | 'auto';
  updateConfig: (updates: Partial<StyleConfig>) => void;

  // dynamicStyleStore兼容接口
  deviceInfo: DeviceInfo;
  interactionState: InteractionState;
  getElementStyle: (elementId: string, baseStyle: any) => any;
}

// 迁移配置接口
export interface MigrationConfig {
  version: number;
  migrations: Record<number, (state: any) => any>;
  validateState: (state: any) => boolean;
}