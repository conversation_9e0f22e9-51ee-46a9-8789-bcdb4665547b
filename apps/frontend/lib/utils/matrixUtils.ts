/**
 * 矩阵计算工具函数
 * 🎯 职责：矩阵数据生成、坐标计算、变换规则等核心计算逻辑
 * 📦 重构来源：从 basicDataStore.ts 中提取的计算函数
 * ✅ 纯函数：无副作用，便于测试和复用
 */

import { globalOptimizedLookup } from '@/lib/data/OptimizedMatrixLookup';
import type {
  BasicColorType,
  CellData,
  GroupType,
  MatrixData,
  MatrixDataPoint
} from '@/lib/types/matrix';
import {
  AVAILABLE_LEVELS,
  GRID_CENTER,
  GRID_DIMENSIONS
} from '@/stores/constants/matrix';

/**
 * 验证坐标是否在有效范围内
 * @param x X坐标
 * @param y Y坐标
 * @returns 是否有效
 */
export const isValidCoordinate = (x: number, y: number): boolean => {
  return x >= -16 && x <= 16 && y >= -16 && y <= 16;
};

/**
 * 生成变换规则名称
 * @param color 颜色
 * @param level 级别
 * @param offsetX X偏移
 * @param offsetY Y偏移
 * @returns 规则名称
 */
export const generateTransformRule = (
  color: BasicColorType,
  level: 1 | 2 | 3 | 4,
  offsetX: number,
  offsetY: number
): string => {
  if (offsetX === 0 && offsetY === 0) {
    return `${color}_level${level}_identity`;
  }
  return `${color}_level${level}_offset_${offsetX}_${offsetY}`;
};

/**
 * 统一的组坐标计算函数 - 支持A到M所有组类型
 * 🚀 优化版本：使用预计算缓存，提升 5-10 倍性能
 * @param group 组类型（A-M）
 * @param color 颜色类型
 * @param level 级别
 * @returns 矩阵数据点数组
 */
export const calculateGroupCoordinates = (
  group: GroupType,
  color: BasicColorType,
  level: 1 | 2 | 3 | 4
): MatrixDataPoint[] => {
  // 使用优化的查找系统获取预计算数据
  const result = globalOptimizedLookup.getByGroupColorLevel(group, color, level);

  if (process.env.NODE_ENV === 'development') {
    console.log(`🚀 [MatrixUtils] 组坐标查询 ${group}-${color}-${level}: ${result.points.length} 个点, 耗时: ${result.queryTime.toFixed(2)}ms, 来源: ${result.source}`);
  }

  return result.points;
};

// 导入智能缓存管理器
import { IntelligentCacheManager } from '@/lib/cache/IntelligentCacheManager';

// 创建专用的矩阵数据缓存管理器
const matrixDataCache = new IntelligentCacheManager<MatrixData>({
  maxSize: 10,
  maxMemoryUsage: 20 * 1024 * 1024, // 20MB
  defaultTTL: 10 * 60 * 1000, // 10分钟
  enableLRU: true,
  enableMetrics: true,
  cleanupInterval: 2 * 60 * 1000 // 2分钟清理一次
});

/**
 * 生成完整的矩阵数据 - 包含A到M所有组的数据点
 * 🚀 优化版本：使用预计算缓存和优化查找系统
 * @param ensureVisibility 是否确保生成的数据与默认可见性配置匹配
 * @param forceRegenerate 是否强制重新生成（忽略缓存）
 * @returns 完整的矩阵数据结构
 */
export const generateMatrixData = (ensureVisibility: boolean = false, forceRegenerate: boolean = false): MatrixData => {
  const startTime = performance.now();

  // 生成缓存键
  const cacheKey = `matrix-data-${ensureVisibility ? 'visible' : 'all'}`;

  // 检查智能缓存
  if (!forceRegenerate) {
    const cachedData = matrixDataCache.get(cacheKey);
    if (cachedData) {
      process.env.NODE_ENV === 'development' && console.log(`🚀 [MatrixUtils] 使用智能缓存数据，耗时: ${(performance.now() - startTime).toFixed(2)}ms`);
      return cachedData;
    }
  }

  process.env.NODE_ENV === 'development' && console.log('🔄 [MatrixUtils] 开始生成优化矩阵数据');

  const matrixData: MatrixData = {
    byCoordinate: new Map(),
    byGroup: {} as Record<GroupType, MatrixDataPoint[]>,
    byColor: {} as Record<BasicColorType, MatrixDataPoint[]>,
    byLevel: {} as Record<1 | 2 | 3 | 4, MatrixDataPoint[]>,
  };

  // 初始化索引结构
  const groups: GroupType[] = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M"];
  const colors: BasicColorType[] = ["black", "red", "orange", "yellow", "green", "cyan", "blue", "purple", "pink"];
  const levels: (1 | 2 | 3 | 4)[] = [1, 2, 3, 4];

  // 预分配数组以提高性能
  groups.forEach(group => {
    matrixData.byGroup[group] = [];
  });

  colors.forEach(color => {
    matrixData.byColor[color] = [];
  });

  levels.forEach(level => {
    matrixData.byLevel[level] = [];
  });

  let totalDataPoints = 0;
  const groupProcessStartTime = performance.now();

  // 生成所有组的数据点
  groups.forEach(group => {
    colors.forEach(color => {
      const availableLevels = AVAILABLE_LEVELS[color];
      availableLevels.forEach(level => {
        const dataPoints = calculateGroupCoordinates(group, color, level as 1 | 2 | 3 | 4);

        if (dataPoints.length === 0) return; // 早期退出，避免无用处理

        totalDataPoints += dataPoints.length;

        dataPoints.forEach(point => {
          // 添加到各种索引中
          matrixData.byGroup[group].push(point);
          matrixData.byColor[color].push(point);
          matrixData.byLevel[point.level].push(point);

          // 添加到坐标索引
          const coordKey = `${point.coords[0]},${point.coords[1]}`;
          if (!matrixData.byCoordinate.has(coordKey)) {
            matrixData.byCoordinate.set(coordKey, []);
          }
          matrixData.byCoordinate.get(coordKey)!.push(point);
        });
      });
    });
  });

  const groupProcessTime = performance.now() - groupProcessStartTime;

  // 如果需要确保可见性，验证生成的数据
  if (ensureVisibility) {
    const hasVisibleData = validateMatrixDataVisibility(matrixData);
    if (!hasVisibleData) {
      process.env.NODE_ENV === 'development' && console.warn('生成的矩阵数据没有可见的数据点，这可能导致空白网格');
    }
  }

  // 更新智能缓存
  matrixDataCache.set(cacheKey, matrixData, {
    tags: ['matrix-data', ensureVisibility ? 'visible' : 'all'],
    dependencies: ['color-visibility', 'group-visibility']
  });

  const totalTime = performance.now() - startTime;
  process.env.NODE_ENV === 'development' && console.log(`✅ [MatrixUtils] 矩阵数据生成完成:`, {
    totalDataPoints,
    coordinateCount: matrixData.byCoordinate.size,
    groupProcessTime: `${groupProcessTime.toFixed(2)}ms`,
    totalTime: `${totalTime.toFixed(2)}ms`
  });

  // 性能警告
  if (totalTime > 50) {
    console.warn(`⚠️ [MatrixUtils] 数据生成耗时较长 (${totalTime.toFixed(2)}ms)，数据点: ${totalDataPoints}`);
  }

  return matrixData;
};

/**
 * 验证矩阵数据是否有可见的数据点
 * @param matrixData 矩阵数据
 * @returns 是否有可见数据点
 */
export const validateMatrixDataVisibility = (matrixData: MatrixData): boolean => {
  // 检查是否有任何坐标有数据点
  return matrixData.byCoordinate.size > 0;
};

/**
 * 生成网格数据 - 供矩阵系统视图渲染使用
 * 创建33x33网格，以中心为(0,0)的坐标系统，序号545为中心点
 * @returns 网格单元格数据数组
 */
export const generateGridData = (): CellData[] => {
  const gridData: CellData[] = [];
  const centerX = GRID_CENTER.X;
  const centerY = GRID_CENTER.Y;

  for (let row = 0; row < GRID_DIMENSIONS.ROWS; row++) {
    for (let col = 0; col < GRID_DIMENSIONS.COLS; col++) {
      // 使用以中心为(0,0)的坐标系统，序号545为中心点
      const x = col - centerX;
      const y = centerY - row;
      const cellId = row * GRID_DIMENSIONS.COLS + col;
      const number = cellId + 1;

      gridData.push({
        id: `cell-${cellId}`,
        row,
        col,
        x,
        y,
        index: cellId,
        color: 'transparent',    // 使用透明而不是灰色
        colorMappingValue: 0,    // 默认映射值
        level: 1,                // 默认级别
        group: null,             // 默认分组
        isActive: false,         // 默认未激活状态
        number,                  // 兼容字段
      });
    }
  }

  return gridData;
};
