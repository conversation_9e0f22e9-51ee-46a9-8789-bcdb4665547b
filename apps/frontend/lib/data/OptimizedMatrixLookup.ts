/**
 * 优化的矩阵查找系统
 * 🎯 核心价值：提供高效的矩阵数据查找接口，整合空间索引和预计算缓存
 * 📦 功能范围：快速查找、批量查询、空间搜索、性能监控
 * ⚡ 性能优化：O(1) 坐标查找、O(log n) 范围查询、智能缓存
 */

import type { BasicColorType, GroupType, MatrixDataPoint } from '@/lib/types/matrix';
import { QuadTreeSpatialIndex, createSpatialIndex, type SpatialPoint, type Bounds } from './SpatialIndexSystem';
import { PrecomputedMatrixCache, globalPrecomputedCache } from './PrecomputedMatrixCache';

// 查找配置接口
export interface LookupConfig {
  enableSpatialIndex: boolean;
  enablePrecomputedCache: boolean;
  enableMetrics: boolean;
  spatialIndexConfig?: {
    maxPointsPerNode: number;
    maxDepth: number;
  };
}

// 查找统计信息
export interface LookupMetrics {
  totalQueries: number;
  spatialIndexHits: number;
  precomputedCacheHits: number;
  averageQueryTime: number;
  memoryUsage: number;
}

// 查询结果接口
export interface QueryResult {
  points: MatrixDataPoint[];
  queryTime: number;
  source: 'spatial-index' | 'precomputed-cache' | 'direct-computation';
}

/**
 * 优化的矩阵查找类
 */
export class OptimizedMatrixLookup {
  private spatialIndex: QuadTreeSpatialIndex;
  private precomputedCache: PrecomputedMatrixCache;
  private coordinateMap: Map<string, MatrixDataPoint[]> = new Map();
  private config: LookupConfig;
  private metrics: LookupMetrics;
  private queryTimes: number[] = [];
  
  constructor(config: Partial<LookupConfig> = {}) {
    this.config = {
      enableSpatialIndex: true,
      enablePrecomputedCache: true,
      enableMetrics: true,
      spatialIndexConfig: {
        maxPointsPerNode: 8,
        maxDepth: 6
      },
      ...config
    };
    
    // 初始化空间索引
    this.spatialIndex = createSpatialIndex({
      maxPointsPerNode: this.config.spatialIndexConfig?.maxPointsPerNode || 8,
      maxDepth: this.config.spatialIndexConfig?.maxDepth || 6,
      enableCache: true,
      enableParallelQuery: false
    });
    
    // 使用全局预计算缓存
    this.precomputedCache = globalPrecomputedCache;
    
    this.metrics = {
      totalQueries: 0,
      spatialIndexHits: 0,
      precomputedCacheHits: 0,
      averageQueryTime: 0,
      memoryUsage: 0
    };
    
    this.initialize();
  }
  
  /**
   * 初始化查找系统
   */
  private async initialize(): Promise<void> {
    const startTime = performance.now();
    
    console.log('🚀 初始化优化矩阵查找系统...');
    
    // 构建空间索引和坐标映射
    await this.buildIndices();
    
    const initTime = performance.now() - startTime;
    console.log(`✅ 矩阵查找系统初始化完成，耗时: ${initTime.toFixed(2)}ms`);
  }
  
  /**
   * 构建索引
   */
  private async buildIndices(): Promise<void> {
    if (!this.config.enableSpatialIndex && !this.config.enablePrecomputedCache) {
      return;
    }
    
    const groups: GroupType[] = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M"];
    const colors: BasicColorType[] = ["black", "red", "orange", "yellow", "green", "cyan", "blue", "purple", "pink"];
    const levels: (1 | 2 | 3 | 4)[] = [1, 2, 3, 4];
    
    for (const group of groups) {
      for (const color of colors) {
        for (const level of levels) {
          // 从预计算缓存获取数据
          const points = this.precomputedCache.get(group, color, level);
          
          for (const point of points) {
            const [x, y] = point.coords;
            const coordKey = `${x},${y}`;
            
            // 构建坐标映射
            if (!this.coordinateMap.has(coordKey)) {
              this.coordinateMap.set(coordKey, []);
            }
            this.coordinateMap.get(coordKey)!.push(point);
            
            // 构建空间索引
            if (this.config.enableSpatialIndex) {
              const spatialPoint: SpatialPoint = {
                x,
                y,
                data: point
              };
              this.spatialIndex.insert(spatialPoint);
            }
          }
        }
      }
    }
  }
  
  /**
   * 按坐标查找 - O(1) 复杂度
   */
  getByCoordinate(x: number, y: number): QueryResult {
    const startTime = performance.now();
    this.metrics.totalQueries++;
    
    const coordKey = `${x},${y}`;
    const points = this.coordinateMap.get(coordKey) || [];
    
    const queryTime = performance.now() - startTime;
    this.recordQueryTime(queryTime);
    
    return {
      points,
      queryTime,
      source: 'direct-computation'
    };
  }
  
  /**
   * 范围查询 - O(log n) 复杂度
   */
  getInRange(minX: number, minY: number, maxX: number, maxY: number): QueryResult {
    const startTime = performance.now();
    this.metrics.totalQueries++;
    
    let points: MatrixDataPoint[] = [];
    let source: QueryResult['source'] = 'direct-computation';
    
    if (this.config.enableSpatialIndex) {
      const bounds: Bounds = { minX, minY, maxX, maxY };
      const spatialPoints = this.spatialIndex.query(bounds);
      points = spatialPoints.map(sp => sp.data);
      source = 'spatial-index';
      this.metrics.spatialIndexHits++;
    } else {
      // 回退到线性搜索
      for (const [coordKey, coordPoints] of this.coordinateMap) {
        const [x, y] = coordKey.split(',').map(Number);
        if (x >= minX && x <= maxX && y >= minY && y <= maxY) {
          points.push(...coordPoints);
        }
      }
    }
    
    const queryTime = performance.now() - startTime;
    this.recordQueryTime(queryTime);
    
    return {
      points,
      queryTime,
      source
    };
  }
  
  /**
   * 按组、颜色、级别查找
   */
  getByGroupColorLevel(group: GroupType, color: BasicColorType, level: 1 | 2 | 3 | 4): QueryResult {
    const startTime = performance.now();
    this.metrics.totalQueries++;
    
    let points: MatrixDataPoint[] = [];
    let source: QueryResult['source'] = 'direct-computation';
    
    if (this.config.enablePrecomputedCache) {
      points = this.precomputedCache.get(group, color, level);
      source = 'precomputed-cache';
      this.metrics.precomputedCacheHits++;
    }
    
    const queryTime = performance.now() - startTime;
    this.recordQueryTime(queryTime);
    
    return {
      points,
      queryTime,
      source
    };
  }
  
  /**
   * 批量坐标查询
   */
  getBatchByCoordinates(coordinates: Array<{x: number, y: number}>): QueryResult {
    const startTime = performance.now();
    this.metrics.totalQueries++;
    
    const allPoints: MatrixDataPoint[] = [];
    
    for (const coord of coordinates) {
      const result = this.getByCoordinate(coord.x, coord.y);
      allPoints.push(...result.points);
    }
    
    const queryTime = performance.now() - startTime;
    this.recordQueryTime(queryTime);
    
    return {
      points: allPoints,
      queryTime,
      source: 'direct-computation'
    };
  }
  
  /**
   * 最近邻查询
   */
  getNearest(x: number, y: number, maxDistance: number = 5): QueryResult {
    const startTime = performance.now();
    this.metrics.totalQueries++;
    
    let points: MatrixDataPoint[] = [];
    let source: QueryResult['source'] = 'direct-computation';
    
    if (this.config.enableSpatialIndex) {
      const nearest = this.spatialIndex.queryNearest(x, y, maxDistance);
      if (nearest) {
        points = [nearest.data];
      }
      source = 'spatial-index';
      this.metrics.spatialIndexHits++;
    } else {
      // 回退到线性搜索
      let minDistance = maxDistance;
      let nearestPoint: MatrixDataPoint | null = null;
      
      for (const [coordKey, coordPoints] of this.coordinateMap) {
        const [px, py] = coordKey.split(',').map(Number);
        const distance = Math.sqrt(Math.pow(px - x, 2) + Math.pow(py - y, 2));
        
        if (distance < minDistance && coordPoints.length > 0) {
          minDistance = distance;
          nearestPoint = coordPoints[0];
        }
      }
      
      if (nearestPoint) {
        points = [nearestPoint];
      }
    }
    
    const queryTime = performance.now() - startTime;
    this.recordQueryTime(queryTime);
    
    return {
      points,
      queryTime,
      source
    };
  }
  
  /**
   * 记录查询时间
   */
  private recordQueryTime(time: number): void {
    if (!this.config.enableMetrics) return;
    
    this.queryTimes.push(time);
    
    // 保持最近1000次查询的记录
    if (this.queryTimes.length > 1000) {
      this.queryTimes.shift();
    }
    
    // 更新平均查询时间
    this.metrics.averageQueryTime = this.queryTimes.reduce((sum, t) => sum + t, 0) / this.queryTimes.length;
  }
  
  /**
   * 获取性能指标
   */
  getMetrics(): LookupMetrics {
    const spatialStats = this.spatialIndex.getStats();
    const precomputedStats = this.precomputedCache.getMetrics();
    
    return {
      ...this.metrics,
      memoryUsage: spatialStats.totalPoints * 32 + precomputedStats.memoryUsage // 估算
    };
  }
  
  /**
   * 获取详细统计信息
   */
  getDetailedStats(): {
    lookup: LookupMetrics;
    spatialIndex: ReturnType<QuadTreeSpatialIndex['getStats']>;
    precomputedCache: ReturnType<PrecomputedMatrixCache['getMetrics']>;
  } {
    return {
      lookup: this.getMetrics(),
      spatialIndex: this.spatialIndex.getStats(),
      precomputedCache: this.precomputedCache.getMetrics()
    };
  }
  
  /**
   * 清空所有缓存和索引
   */
  clear(): void {
    this.spatialIndex.clear();
    this.coordinateMap.clear();
    this.queryTimes = [];
    this.metrics = {
      totalQueries: 0,
      spatialIndexHits: 0,
      precomputedCacheHits: 0,
      averageQueryTime: 0,
      memoryUsage: 0
    };
  }
}

// 全局优化查找实例
export const globalOptimizedLookup = new OptimizedMatrixLookup({
  enableSpatialIndex: true,
  enablePrecomputedCache: true,
  enableMetrics: true
});

/**
 * 创建优化查找实例的工厂函数
 */
export function createOptimizedLookup(config?: Partial<LookupConfig>): OptimizedMatrixLookup {
  return new OptimizedMatrixLookup(config);
}
