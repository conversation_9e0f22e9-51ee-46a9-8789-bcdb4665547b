/**
 * 空间索引系统
 * 🎯 核心价值：实现高效的空间查询，将查找复杂度从 O(n) 降到 O(log n)
 * 📦 功能范围：四叉树索引、范围查询、最近邻搜索、批量操作
 * ⚡ 性能优化：空间分区、智能缓存、并行查询
 */

import type { MatrixDataPoint } from '@/lib/types/matrix';

// 空间边界接口
export interface Bounds {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
}

// 空间点接口
export interface SpatialPoint {
  x: number;
  y: number;
  data: MatrixDataPoint;
}

// 四叉树节点接口
export interface QuadTreeNode {
  bounds: Bounds;
  points: SpatialPoint[];
  children: QuadTreeNode[] | null;
  level: number;
}

// 空间索引配置
export interface SpatialIndexConfig {
  maxPointsPerNode: number;
  maxDepth: number;
  enableCache: boolean;
  enableParallelQuery: boolean;
}

/**
 * 四叉树空间索引类
 */
export class QuadTreeSpatialIndex {
  private root: QuadTreeNode;
  private config: SpatialIndexConfig;
  private queryCache: Map<string, SpatialPoint[]> = new Map();
  private totalPoints: number = 0;
  
  constructor(bounds: Bounds, config: Partial<SpatialIndexConfig> = {}) {
    this.config = {
      maxPointsPerNode: 10,
      maxDepth: 8,
      enableCache: true,
      enableParallelQuery: false,
      ...config
    };
    
    this.root = {
      bounds,
      points: [],
      children: null,
      level: 0
    };
  }
  
  /**
   * 插入空间点
   */
  insert(point: SpatialPoint): void {
    this.insertIntoNode(this.root, point);
    this.totalPoints++;
    
    // 清除相关缓存
    if (this.config.enableCache) {
      this.invalidateCache(point);
    }
  }
  
  /**
   * 批量插入空间点
   */
  insertBatch(points: SpatialPoint[]): void {
    for (const point of points) {
      this.insertIntoNode(this.root, point);
    }
    this.totalPoints += points.length;
    
    // 批量清除缓存
    if (this.config.enableCache) {
      this.queryCache.clear();
    }
  }
  
  /**
   * 向节点插入点
   */
  private insertIntoNode(node: QuadTreeNode, point: SpatialPoint): void {
    // 检查点是否在节点边界内
    if (!this.pointInBounds(point, node.bounds)) {
      return;
    }
    
    // 如果节点没有子节点且点数未超限，直接添加
    if (!node.children && node.points.length < this.config.maxPointsPerNode) {
      node.points.push(point);
      return;
    }
    
    // 如果需要分割节点
    if (!node.children && node.level < this.config.maxDepth) {
      this.subdivideNode(node);
    }
    
    // 如果有子节点，递归插入
    if (node.children) {
      for (const child of node.children) {
        this.insertIntoNode(child, point);
      }
    } else {
      // 达到最大深度，强制添加到当前节点
      node.points.push(point);
    }
  }
  
  /**
   * 分割节点为四个子节点
   */
  private subdivideNode(node: QuadTreeNode): void {
    const { minX, minY, maxX, maxY } = node.bounds;
    const midX = (minX + maxX) / 2;
    const midY = (minY + maxY) / 2;
    
    node.children = [
      // 西北
      { bounds: { minX, minY: midY, maxX: midX, maxY }, points: [], children: null, level: node.level + 1 },
      // 东北
      { bounds: { minX: midX, minY: midY, maxX, maxY }, points: [], children: null, level: node.level + 1 },
      // 西南
      { bounds: { minX, minY, maxX: midX, maxY: midY }, points: [], children: null, level: node.level + 1 },
      // 东南
      { bounds: { minX: midX, minY, maxX, maxY: midY }, points: [], children: null, level: node.level + 1 }
    ];
    
    // 重新分配现有点到子节点
    for (const point of node.points) {
      for (const child of node.children) {
        this.insertIntoNode(child, point);
      }
    }
    
    // 清空当前节点的点（已分配到子节点）
    node.points = [];
  }
  
  /**
   * 范围查询
   */
  query(bounds: Bounds): SpatialPoint[] {
    const cacheKey = this.config.enableCache ? this.boundsToString(bounds) : '';
    
    // 检查缓存
    if (this.config.enableCache && cacheKey) {
      const cached = this.queryCache.get(cacheKey);
      if (cached) {
        return cached;
      }
    }
    
    const results: SpatialPoint[] = [];
    this.queryNode(this.root, bounds, results);
    
    // 缓存结果
    if (this.config.enableCache && cacheKey) {
      this.queryCache.set(cacheKey, results);
    }
    
    return results;
  }
  
  /**
   * 查询节点
   */
  private queryNode(node: QuadTreeNode, bounds: Bounds, results: SpatialPoint[]): void {
    // 检查节点边界是否与查询边界相交
    if (!this.boundsIntersect(node.bounds, bounds)) {
      return;
    }
    
    // 检查节点中的点
    for (const point of node.points) {
      if (this.pointInBounds(point, bounds)) {
        results.push(point);
      }
    }
    
    // 递归查询子节点
    if (node.children) {
      for (const child of node.children) {
        this.queryNode(child, bounds, results);
      }
    }
  }
  
  /**
   * 精确坐标查询
   */
  queryExact(x: number, y: number): SpatialPoint[] {
    return this.query({ minX: x, minY: y, maxX: x, maxY: y });
  }
  
  /**
   * 最近邻查询
   */
  queryNearest(x: number, y: number, maxDistance: number = Infinity): SpatialPoint | null {
    let nearest: SpatialPoint | null = null;
    let minDistance = maxDistance;
    
    const searchBounds: Bounds = {
      minX: x - maxDistance,
      minY: y - maxDistance,
      maxX: x + maxDistance,
      maxY: y + maxDistance
    };
    
    const candidates = this.query(searchBounds);
    
    for (const point of candidates) {
      const distance = Math.sqrt(
        Math.pow(point.x - x, 2) + Math.pow(point.y - y, 2)
      );
      
      if (distance < minDistance) {
        minDistance = distance;
        nearest = point;
      }
    }
    
    return nearest;
  }
  
  /**
   * 检查点是否在边界内
   */
  private pointInBounds(point: SpatialPoint, bounds: Bounds): boolean {
    return point.x >= bounds.minX && point.x <= bounds.maxX &&
           point.y >= bounds.minY && point.y <= bounds.maxY;
  }
  
  /**
   * 检查两个边界是否相交
   */
  private boundsIntersect(bounds1: Bounds, bounds2: Bounds): boolean {
    return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
             bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
  }
  
  /**
   * 边界转字符串（用于缓存键）
   */
  private boundsToString(bounds: Bounds): string {
    return `${bounds.minX},${bounds.minY},${bounds.maxX},${bounds.maxY}`;
  }
  
  /**
   * 使缓存失效
   */
  private invalidateCache(point: SpatialPoint): void {
    // 简单策略：清除所有缓存
    // 更复杂的策略可以只清除相关区域的缓存
    this.queryCache.clear();
  }
  
  /**
   * 获取统计信息
   */
  getStats(): {
    totalPoints: number;
    maxDepth: number;
    nodeCount: number;
    cacheSize: number;
  } {
    return {
      totalPoints: this.totalPoints,
      maxDepth: this.getMaxDepth(this.root),
      nodeCount: this.getNodeCount(this.root),
      cacheSize: this.queryCache.size
    };
  }
  
  /**
   * 获取最大深度
   */
  private getMaxDepth(node: QuadTreeNode): number {
    if (!node.children) {
      return node.level;
    }
    
    return Math.max(...node.children.map(child => this.getMaxDepth(child)));
  }
  
  /**
   * 获取节点数量
   */
  private getNodeCount(node: QuadTreeNode): number {
    let count = 1;
    
    if (node.children) {
      for (const child of node.children) {
        count += this.getNodeCount(child);
      }
    }
    
    return count;
  }
  
  /**
   * 清空索引
   */
  clear(): void {
    this.root.points = [];
    this.root.children = null;
    this.queryCache.clear();
    this.totalPoints = 0;
  }
}

/**
 * 创建空间索引的工厂函数
 */
export function createSpatialIndex(config?: Partial<SpatialIndexConfig>): QuadTreeSpatialIndex {
  // 矩阵坐标范围：-16 到 16
  const bounds: Bounds = {
    minX: -16,
    minY: -16,
    maxX: 16,
    maxY: 16
  };
  
  return new QuadTreeSpatialIndex(bounds, config);
}
