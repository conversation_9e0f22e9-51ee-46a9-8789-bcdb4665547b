/**
 * 统一网格渲染引擎
 * 🎯 核心价值：统一管理所有网格渲染逻辑，解决分散渲染导致的bug
 * 📦 功能范围：单元格颜色、内容、样式的统一计算和缓存
 * ⚡ 性能优化：渲染缓存、批量处理、智能重渲染
 * 🔧 重构优化：使用统一的服务层，避免重复逻辑
 */

import type { BaseDisplayMode } from '@/components/grid-system';
import { ColorMappingService } from '@/lib/services/ColorMappingService';
import { SpecialCoordinateService } from '@/lib/services/SpecialCoordinateService';
import type { CellData } from '@/lib/types/grid';
import { DEFAULT_COLOR_VALUES } from '@/stores/constants/matrix';
import { globalOptimizedLookup } from '@/lib/data/OptimizedMatrixLookup';
import React from 'react';

// 渲染配置接口
export interface RenderingConfig {
  displayMode: BaseDisplayMode;
  colorModeEnabled: boolean;
  cellSize: number;
  cellGap: number;
  showBorders: boolean;
  enableAnimations: boolean;
  opacity: number;
}

// 单元格渲染数据接口
export interface CellRenderData {
  color: string | null;
  content: string | null;
  style: React.CSSProperties;
  className: string;
  isActive: boolean;
  isVisible: boolean;
}

// 渲染缓存键
type CacheKey = string;

// 渲染引擎类
export class GridRenderingEngine {
  private renderCache = new Map<CacheKey, CellRenderData>();
  private config: RenderingConfig;
  private lastConfigHash: string = '';

  constructor(config: RenderingConfig) {
    this.config = config;
    this.updateConfigHash();
  }

  /**
   * 更新渲染配置
   */
  updateConfig(newConfig: Partial<RenderingConfig>): void {
    const oldHash = this.lastConfigHash;
    this.config = { ...this.config, ...newConfig };
    this.updateConfigHash();

    // 如果配置发生变化，清空缓存
    if (this.lastConfigHash !== oldHash) {
      this.clearCache();
    }
  }

  /**
   * 获取单元格完整渲染数据
   */
  getCellRenderData(cell: CellData): CellRenderData {
    const cacheKey = this.generateCacheKey(cell);

    // 尝试从缓存获取
    const cached = this.renderCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    // 计算渲染数据
    const renderData: CellRenderData = {
      color: this.calculateCellColor(cell),
      content: this.calculateCellContent(cell),
      style: this.calculateCellStyle(cell),
      className: this.calculateCellClassName(cell),
      isActive: this.calculateCellActive(cell),
      isVisible: this.calculateCellVisible(cell),
    };

    // 缓存结果
    this.renderCache.set(cacheKey, renderData);
    return renderData;
  }

  /**
   * 批量获取单元格渲染数据
   */
  getBatchCellRenderData(cells: CellData[]): Map<string, CellRenderData> {
    const result = new Map<string, CellRenderData>();

    for (const cell of cells) {
      const key = `${cell.x}-${cell.y}`;
      result.set(key, this.getCellRenderData(cell));
    }

    return result;
  }

  /**
   * 计算单元格颜色
   */
  private calculateCellColor(cell: CellData): string | null {
    // 检查是否为特殊黑色坐标
    if (SpecialCoordinateService.getCharacter(cell.x, cell.y)) {
      return '#000000';
    }

    // 如果没有激活或不可见，返回透明
    if (!cell.isActive || !this.calculateCellVisible(cell)) {
      return null;
    }

    // 根据显示模式返回颜色
    if (this.config.colorModeEnabled) {
      // 优先使用 colorMappingValue 来确定颜色
      if (cell.colorMappingValue && cell.colorMappingValue >= 1 && cell.colorMappingValue <= 8) {
        const colorType = ColorMappingService.getColorTypeFromMappingValue(cell.colorMappingValue);
        if (colorType) {
          const colorValue = ColorMappingService.getColorForValue(colorType, cell.level, DEFAULT_COLOR_VALUES);
          if (colorValue) {
            return colorValue;
          }
        }
      }

      // 如果 colorMappingValue 无效，则使用 cell.color（包括 "transparent"）
      if (cell.color) {
        return cell.color;
      }
    }

    return null;
  }

  /**
   * 计算单元格内容
   */
  private calculateCellContent(cell: CellData): string | null {
    // 首先检查是否为特殊黑色坐标，优先显示字母
    const specialChar = SpecialCoordinateService.getCharacter(cell.x, cell.y);
    if (specialChar) {
      return specialChar;
    }

    switch (this.config.displayMode) {
      case 'coordinates':
        return `${cell.x},${cell.y}`;
      case 'value':
        // 彩色单元格显示数字1-8（根据colorMappingValue）
        if (cell.colorMappingValue && cell.colorMappingValue >= 1 && cell.colorMappingValue <= 8) {
          return cell.colorMappingValue.toString();
        }
        return null;
      case 'color':
        return null; // 颜色模式仅显示颜色，无文字覆盖
      default:
        return null;
    }
  }



  /**
   * 计算单元格样式
   */
  private calculateCellStyle(cell: CellData): React.CSSProperties {
    const color = this.calculateCellColor(cell);

    return {
      width: `${this.config.cellSize}px`,
      height: `${this.config.cellSize}px`,
      backgroundColor: color || 'transparent',
      opacity: this.calculateCellActive(cell) ? this.config.opacity : 0.3,
      border: this.config.showBorders ? '1px solid #e5e7eb' : 'none',
      transition: this.config.enableAnimations ? 'all 0.2s ease' : 'none',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '10px',
      color: this.getTextColor(color),
      cursor: 'pointer',
      userSelect: 'none',
    };
  }

  /**
   * 计算单元格类名
   */
  private calculateCellClassName(cell: CellData): string {
    const classes = ['grid-cell'];

    if (this.calculateCellActive(cell)) {
      classes.push('active');
    }

    if (cell.group) {
      classes.push(`group-${cell.group}`);
    }

    if (this.isSpecialBlackCoordinate(cell.x, cell.y)) {
      classes.push('special-black');
    }

    return classes.join(' ');
  }

  /**
   * 计算单元格是否激活
   */
  private calculateCellActive(cell: CellData): boolean {
    return cell.isActive;
  }

  /**
   * 计算单元格是否可见
   */
  private calculateCellVisible(_cell: CellData): boolean {
    return true; // 基础实现，可以根据需要扩展
  }

  /**
   * 检查是否为特殊黑色坐标
   */
  private isSpecialBlackCoordinate(x: number, y: number): boolean {
    return SpecialCoordinateService.getCharacter(x, y) !== null;
  }

  /**
   * 获取文本颜色
   */
  private getTextColor(backgroundColor: string | null): string {
    if (!backgroundColor || backgroundColor === 'transparent') {
      return '#374151';
    }

    // 简单的对比度计算
    if (backgroundColor === '#000000') {
      return '#ffffff';
    }

    return '#000000';
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(cell: CellData): CacheKey {
    return `${cell.x}-${cell.y}-${this.lastConfigHash}-${cell.isActive}-${cell.color}-${cell.level}`;
  }

  /**
   * 更新配置哈希
   */
  private updateConfigHash(): void {
    this.lastConfigHash = JSON.stringify(this.config);
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.renderCache.clear();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.renderCache.size,
      hitRate: 0, // TODO: 实现命中率统计
    };
  }
}

// 默认渲染配置
export const DEFAULT_RENDERING_CONFIG: RenderingConfig = {
  displayMode: 'coordinates',
  colorModeEnabled: false,
  cellSize: 24,
  cellGap: 2,
  showBorders: true,
  enableAnimations: true,
  opacity: 1,
};

// 创建渲染引擎实例的工厂函数
export function createRenderingEngine(config?: Partial<RenderingConfig>): GridRenderingEngine {
  const finalConfig = { ...DEFAULT_RENDERING_CONFIG, ...config };
  return new GridRenderingEngine(finalConfig);
}
